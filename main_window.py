#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

try:
    from PyQt5.QtWidgets import QMainWindow, QWidget, QHBoxLayout, QStatusBar, QFrame, QVBoxLayout, QAction, QFileDialog, \
    QMessageBox, QApplication
    from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
    QT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtWidgets import QMainWindow, QWidget, QHBoxLayout, QStatusBar, QFrame, QVBoxLayout, QAction, QFileDialog, \
    QMessageBox, QApplication
        from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        QT_VERSION = 6
    except ImportError:
        print("错误: 未安装PyQt5或PyQt6")
        sys.exit(1)

import vtk
from typing import Optional

from data_manager import DataManager
from visualization_manager import VisualizationManager
from control_panel import ControlPanel
from progress_manager import ProgressManager


class CFXVTKVisualizerRefactored(QMainWindow):
    """重构后的CFX VTK可视化器主窗口"""
    
    def __init__(self):
        super().__init__()
        
        # 核心组件
        self.data_manager = DataManager()
        self.visualization_manager = VisualizationManager(self.data_manager)
        self.progress_manager = ProgressManager(self)
        
        # VTK组件
        self.renderer = None
        self.render_window = None
        self.interactor = None
        self.vtk_widget = None
        
        # UI组件
        self.control_panel = None
        
        self.setup_ui()
        self.setup_vtk()
        self.connect_signals()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("CFX VTK 流体可视化工具")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧控制面板
        self.control_panel = ControlPanel()
        main_layout.addWidget(self.control_panel, 0)
        
        # 右侧VTK渲染窗口
        vtk_frame = self.create_vtk_frame()
        main_layout.addWidget(vtk_frame, 1)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
        # 创建菜单栏
        self.create_menu_bar()
        
    def create_vtk_frame(self) -> QFrame:
        """创建VTK渲染框架"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        
        # 创建VTK渲染窗口
        self.vtk_widget = QVTKRenderWindowInteractor(frame)
        layout.addWidget(self.vtk_widget)
        
        return frame
        
    def setup_vtk(self):
        """设置VTK组件"""
        # 初始化VTK组件
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)  # 深蓝色背景
        
        self.render_window = self.vtk_widget.GetRenderWindow()
        self.render_window.AddRenderer(self.renderer)
        
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        
        # 设置交互样式
        style = vtk.vtkInteractorStyleTrackballCamera()
        self.interactor.SetInteractorStyle(style)
        
        # 设置可视化管理器的VTK组件
        self.visualization_manager.setup_renderer(self.renderer, self.render_window, self.interactor)
        self.visualization_manager.setup_visualizers()
        
    def connect_signals(self):
        """连接信号和槽"""
        # 数据加载信号
        self.control_panel.load_mesh_requested.connect(self.load_mesh_data)
        self.control_panel.load_steady_requested.connect(self.load_steady_data)
        self.control_panel.load_transient_requested.connect(self.load_transient_data)
        
        # 可视化控制信号
        self.control_panel.create_visualization_requested.connect(self.create_visualization)
        self.control_panel.update_display_requested.connect(self.update_display)
        self.control_panel.render_mesh_only_requested.connect(self.render_mesh_only)
        self.control_panel.opacity_changed.connect(self.update_opacity)
        self.control_panel.create_3d_streamlines_clicked.connect(self.create_3d_streamlines)
        
        # 显示选项变化信号
        self.control_panel.display_options_changed.connect(self.on_display_options_changed)
        self.control_panel.region_selection_changed.connect(self.on_region_selection_changed)
        self.control_panel.visualization_parameters_changed.connect(self.on_parameters_changed)
        
        # 导出功能信号
        self.control_panel.save_image_requested.connect(self.save_image)
        self.control_panel.export_vtk_requested.connect(self.export_vtk)
        
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        load_action = QAction('加载数据...', self)
        load_action.triggered.connect(self.load_mesh_data)
        file_menu.addAction(load_action)
        
        file_menu.addSeparator()
        
        save_action = QAction('保存图像...', self)
        save_action.triggered.connect(self.save_image)
        file_menu.addAction(save_action)
        
        export_action = QAction('导出VTK...', self)
        export_action.triggered.connect(self.export_vtk)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图')
        
        reset_camera_action = QAction('重置相机', self)
        reset_camera_action.triggered.connect(self.reset_camera)
        view_menu.addAction(reset_camera_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def load_mesh_data(self):
        """加载网格数据"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "选择网格文件",
            "demo_data/case1" if os.path.exists("demo_data/case1") else ".",
            "CSV files (*.csv);;All files (*.*)"
        )
        
        if filename:
            def load_task(callback):
                # 附加文件名参数
                return self.data_manager.load_mesh_data(filename, callback)
                
            success = self.progress_manager.show_progress_dialog("加载网格数据", load_task)
            
            if success:
                total_points = sum(len(region.data) for region in self.data_manager.mesh_data.values())
                self.status_bar.showMessage(f"✓ 已加载网格数据: {len(self.data_manager.mesh_data)} 个区域, {total_points} 个点")
                
                # 更新区域控制
                region_names = self.data_manager.get_all_region_names()
                self.control_panel.update_region_controls(region_names)
                
                # 提示用户
                QMessageBox.information(self, "网格数据加载成功",
                    "网格数据已加载完成！\n\n您可以：\n1. 点击'仅渲染网格'按钮直接查看网格\n2. 或加载稳态/瞬态数据后进行完整可视化")
            else:
                QMessageBox.critical(self, "加载错误", "加载网格数据失败，请检查文件格式")
                
    def load_steady_data(self):
        """加载稳态数据"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "选择稳态结果文件",
            "demo_data/case1" if os.path.exists("demo_data/case1") else ".",
            "CSV files (*.csv);;All files (*.*)"
        )
        
        if filename:
            def load_task(callback):
                return self.data_manager.load_steady_data(filename, callback)
                
            success = self.progress_manager.show_progress_dialog("加载稳态数据", load_task)
            
            if success:
                total_points = sum(len(region.data) for region in self.data_manager.steady_data.values())
                self.status_bar.showMessage(f"✓ 已加载稳态数据: {len(self.data_manager.steady_data)} 个区域, {total_points} 个点")
                
                # 更新区域控制
                region_names = self.data_manager.get_all_region_names()
                self.control_panel.update_region_controls(region_names)
                
                # 显示压力数据信息
                pressure_info = self.data_manager.get_pressure_info("steady")
                self.progress_manager.show_data_info("稳态数据加载成功", pressure_info)
            else:
                QMessageBox.critical(self, "加载错误", "加载稳态数据失败，请检查文件格式")
                
    def load_transient_data(self):
        """加载瞬态数据"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "选择瞬态结果文件",
            "demo_data/case1" if os.path.exists("demo_data/case1") else ".",
            "CSV files (*.csv);;All files (*.*)"
        )
        
        if filename:
            def load_task(callback):
                return self.data_manager.load_transient_data(filename, callback)
                
            success = self.progress_manager.show_progress_dialog("加载瞬态数据", load_task)
            
            if success:
                total_points = sum(len(region.data) for region in self.data_manager.transient_data.values())
                self.status_bar.showMessage(f"✓ 已加载瞬态数据: {len(self.data_manager.transient_data)} 个区域, {total_points} 个点")
                
                # 更新区域控制
                region_names = self.data_manager.get_all_region_names()
                self.control_panel.update_region_controls(region_names)
                
                # 显示压力数据信息
                pressure_info = self.data_manager.get_pressure_info("transient")
                self.progress_manager.show_data_info("瞬态数据加载成功", pressure_info)
            else:
                QMessageBox.critical(self, "加载错误", "加载瞬态数据失败，请检查文件格式")

    def create_visualization(self):
        """创建可视化"""
        # 获取控制面板参数
        data_source = self.control_panel.get_data_source()
        selected_regions = self.control_panel.get_selected_regions()
        display_options = self.control_panel.get_display_options()
        viz_params = self.control_panel.get_visualization_parameters()

        # 检查数据
        if not self.data_manager.has_flow_data(data_source) and not display_options['show_mesh']:
            QMessageBox.critical(self, "错误",
                f"请先加载{'稳态' if data_source == 'steady' else '瞬态'}数据，或加载网格数据并勾选'网格面片'")
            return

        # 如果只显示网格，检查是否有任何可用的网格数据
        if display_options['show_mesh'] and not self.data_manager.has_any_mesh_data():
            QMessageBox.critical(self, "错误", "没有可用的网格数据")
            return

        if not selected_regions:
            QMessageBox.information(self, "提示", "请选择要可视化的区域")
            return

        # 设置可视化参数
        self.visualization_manager.set_visualization_parameters(
            sampling_rate=int(viz_params['sampling_rate']),
            streamline_density=int(viz_params['streamline_density']),
            vector_scale=viz_params['vector_scale'],
            contour_count=int(viz_params['contour_count']),
            contour_line_width=int(viz_params['contour_line_width'])
        )

        # 创建可视化
        def create_task(callback):
            # 创建一个带取消检查的进度回调函数
            def progress_func(progress, message="", detail=""):
                if callback and hasattr(callback, 'callback') and callback.callback:
                    callback.callback(progress, message, detail)
                    # 检查是否被取消
                    if hasattr(callback, 'is_cancelled') and callback.is_cancelled():
                        raise InterruptedError("操作被用户取消")

            try:
                return self.visualization_manager.create_visualization(
                    data_source=data_source,
                    selected_regions=selected_regions,
                    show_pressure=display_options['show_pressure'],
                    show_contours=display_options['show_contours'],
                    show_streamlines=display_options['show_streamlines'],
                    show_vectors=display_options['show_vectors'],
                    show_mesh=display_options['show_mesh'],
                    progress_callback=progress_func
                )
            except InterruptedError as e:
                print(f"可视化创建被取消: {e}")
                return False

        success = self.progress_manager.show_progress_dialog("创建可视化", create_task)

        if success:
            self.status_bar.showMessage(f"✓ 可视化创建完成 - {len(selected_regions)} 个区域")
        else:
            QMessageBox.critical(self, "错误", "创建可视化失败")

    def update_display(self):
        """更新显示"""
        selected_regions = self.control_panel.get_selected_regions()
        display_options = self.control_panel.get_display_options()

        self.visualization_manager.update_visualization_display(
            selected_regions=selected_regions,
            show_pressure=display_options['show_pressure'],
            show_contours=display_options['show_contours'],
            show_streamlines=display_options['show_streamlines'],
            show_vectors=display_options['show_vectors'],
            show_mesh=display_options['show_mesh']
        )

        self.status_bar.showMessage(f"✓ 显示已更新 - {len(selected_regions)} 个区域")

    def update_opacity(self):
        """更新透明度"""
        try:
            opacity_params = self.control_panel.get_opacity_parameters()
            self.visualization_manager.set_all_opacity(opacity_params)
            self.status_bar.showMessage("✓ 透明度已更新")
        except Exception as e:
            print(f"更新透明度失败: {e}")
            self.status_bar.showMessage("⚠️ 透明度更新失败")

    def render_mesh_only(self):
        """仅渲染网格 - 优化版本"""
        if not self.data_manager.has_any_mesh_data():
            QMessageBox.information(self, "提示", "请先加载网格数据或结果数据")
            return

        selected_regions = self.control_panel.get_selected_regions()
        if not selected_regions:
            QMessageBox.information(self, "提示", "请选择要显示的区域")
            return

        try:
            # 使用智能网格渲染方法
            success = self.visualization_manager.render_mesh_only_smart(selected_regions)

            if success:
                visible_count = sum(1 for region_name, actor in self.visualization_manager.mesh_actors.items()
                                  if actor and actor.GetVisibility())
                self.status_bar.showMessage(f"✓ 网格渲染完成 - {visible_count} 个区域可见")
            else:
                QMessageBox.information(self, "提示", "选中的区域没有网格数据")

        except Exception as e:
            print(f"网格渲染失败: {e}")
            QMessageBox.critical(self, "错误", f"网格渲染失败: {str(e)}")

    def on_display_options_changed(self):
        """显示选项变化处理 - 优化版本"""
        # 显示选项变化时，使用区域选择变化的处理逻辑
        self.on_region_selection_changed()

    def on_region_selection_changed(self):
        """区域选择变化处理 - 优化版本"""
        selected_regions = self.control_panel.get_selected_regions()
        display_options = self.control_panel.get_display_options()

        # 使用智能更新方法
        success = self.visualization_manager.update_region_selection_smart(
            selected_regions,
            show_pressure=display_options.get('show_pressure', True),
            show_contours=display_options.get('show_contours', False),
            show_streamlines=display_options.get('show_streamlines', True),
            show_vectors=display_options.get('show_vectors', False),
            show_mesh=display_options.get('show_mesh', False)
        )

        if success:
            self.status_bar.showMessage(f"已更新区域显示 - {len(selected_regions)} 个区域")
        else:
            # 如果没有可视化对象，提示用户
            if not self.visualization_manager.has_flow_actors() and not self.visualization_manager.has_mesh_actors():
                self.status_bar.showMessage("请先创建可视化对象")
            else:
                self.status_bar.showMessage("区域显示更新完成")

    def on_parameters_changed(self):
        """参数变化处理"""
        # 参数变化时，需要重新创建可视化对象
        # 这里可以添加智能更新逻辑
        pass

    def save_image(self):
        """保存图像"""
        if self.render_window is None:
            QMessageBox.critical(self, "错误", "请先创建可视化")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "保存图像", "",
            "PNG files (*.png);;JPEG files (*.jpg);;All files (*.*)"
        )

        if filename:
            try:
                # 创建窗口到图像过滤器
                w2if = vtk.vtkWindowToImageFilter()
                w2if.SetInput(self.render_window)
                w2if.Update()

                # 写入图像
                if filename.lower().endswith('.png'):
                    writer = vtk.vtkPNGWriter()
                elif filename.lower().endswith(('.jpg', '.jpeg')):
                    writer = vtk.vtkJPEGWriter()
                else:
                    writer = vtk.vtkPNGWriter()
                    filename += '.png'

                writer.SetFileName(filename)
                writer.SetInputConnection(w2if.GetOutputPort())
                writer.Write()

                self.status_bar.showMessage(f"图像已保存: {filename}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存图像失败: {str(e)}")

    def export_vtk(self):
        """导出VTK文件"""
        if not hasattr(self.visualization_manager, 'region_vtk_grids') or not self.visualization_manager.region_vtk_grids:
            QMessageBox.critical(self, "错误", "请先创建可视化")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "导出VTK文件", "",
            "VTU files (*.vtu);;VTK files (*.vtk);;All files (*.*)"
        )

        if filename:
            try:
                # 导出第一个区域的VTK网格
                first_grid = list(self.visualization_manager.region_vtk_grids.values())[0]

                if filename.lower().endswith('.vtu'):
                    writer = vtk.vtkXMLUnstructuredGridWriter()
                else:
                    writer = vtk.vtkUnstructuredGridWriter()

                writer.SetFileName(filename)
                writer.SetInputData(first_grid)
                writer.Write()

                self.status_bar.showMessage(f"VTK文件已导出: {filename}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出VTK文件失败: {str(e)}")

    def reset_camera(self):
        """重置相机"""
        if self.renderer:
            self.renderer.ResetCamera()
            self.render_window.Render()

    def create_3d_streamlines(self):
        """创建3D入口流线"""
        try:
            # 检查是否有数据
            if not self.data_manager.has_steady_data() and not self.data_manager.has_transient_data():
                QMessageBox.warning(self, "警告", "请先加载稳态或瞬态数据")
                return

            # 选择数据源
            data_source = "steady" if self.data_manager.has_steady_data() else "transient"

            # 显示进度对话框

            success = self.visualization_manager.create_3d_streamlines_from_inlets(
                data_source=data_source,
                max_points=25,
                sampling_method="vertex"
            )

            if success:
                self.status_bar.showMessage("✓ 3D入口流线创建成功")
                # 刷新渲染
                self.visualization_manager.render()
                QMessageBox.information(self, "成功",
                    "3D入口流线创建成功！\n\n"
                    "流线从所有入口开始，穿越整个计算域。\n"
                    "您可以旋转视图来查看3D流线的完整路径。")
            else:
                self.status_bar.showMessage("❌ 3D入口流线创建失败")
                QMessageBox.warning(self, "失败",
                    "3D入口流线创建失败。\n\n"
                    "可能的原因：\n"
                    "1. 网格数据不完整\n"
                    "2. 速度场数据有问题\n"
                    "3. 入口区域未找到\n\n"
                    "请检查控制台输出获取详细信息。")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建3D流线时发生错误：{str(e)}")
            print(f"创建3D流线失败: {e}")
            import traceback
            traceback.print_exc()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         "CFX VTK 流体可视化工具 (重构版)\n\n"
                         "基于面向对象架构的CFX仿真结果可视化工具\n"
                         "支持流体流速迹线云图和压力分布云图\n\n"
                         "版本: 3.0 (重构版)")

    def closeEvent(self, event):
        """关闭事件处理"""
        if self.vtk_widget:
            self.vtk_widget.close()
        event.accept()


def main():
    """主函数"""
    # 检查VTK是否可用
    try:
        import vtk
        print(f"VTK版本: {vtk.vtkVersion.GetVTKVersion()}")
    except ImportError:
        print("错误: 未安装VTK库")
        print("请运行: pip install vtk")
        sys.exit(1)

    # 创建Qt应用
    app = QApplication(sys.argv)
    app.setApplicationName("CFX VTK 流体可视化工具")
    app.setApplicationVersion("3.0")

    # 创建并显示主窗口
    window = CFXVTKVisualizerRefactored()
    window.show()

    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
