#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFX数据解析器
负责解析CFX CSV文件格式，支持多区域数据
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Optional, Callable, Any
from data_models import RegionData


class ProgressCallback:
    """进度回调接口"""
    
    def __init__(self, callback: Optional[Callable[[int, str, str], None]] = None):
        self.callback = callback
        self.cancelled = False
        
    def update(self, progress: int, message: str = "", detail: str = ""):
        """更新进度"""
        if self.callback:
            self.callback(progress, message, detail)
            
    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self.cancelled
        
    def cancel(self):
        """取消操作"""
        self.cancelled = True


class CFXDataParser:
    """CFX CSV文件解析器"""
    
    def __init__(self):
        self.encoding_options = ['utf-8', 'latin-1', 'gbk', 'cp1252']
        
    def parse_file(self, filename: str, progress_callback: Optional[ProgressCallback] = None) -> Dict[str, RegionData]:
        """
        解析CFX CSV文件

        Args:
            filename: 文件路径
            progress_callback: 进度回调

        Returns:
            Dict[str, RegionData]: 区域名称到RegionData的映射
        """
        if progress_callback:
            progress_callback.update(5, "正在读取文件...")

        # 尝试使用新的稳健解析方法
        try:
            regions = self._parse_file_robust(filename, progress_callback)
            if regions:
                if progress_callback:
                    progress_callback.update(100, "解析完成")
                return regions
        except Exception as e:
            print(f"稳健解析方法失败，回退到原始方法: {e}")

        # 回退到原始解析方法
        # 读取文件内容
        lines = self._read_file_with_encoding(filename)
        if not lines:
            raise ValueError("无法读取文件或文件为空")

        if progress_callback:
            progress_callback.update(15, f"文件读取完成，共 {len(lines)} 行")

        # 尝试多区域解析
        regions = self._parse_multi_region(lines, progress_callback)

        if not regions:
            # 回退到单区域解析
            if progress_callback:
                progress_callback.update(80, "尝试单区域解析...")
            single_data = self._parse_single_region(lines)
            if single_data:
                regions = {"Default": single_data}

        if not regions:
            raise ValueError("无法解析文件，请检查文件格式")

        if progress_callback:
            progress_callback.update(100, "解析完成")

        return regions

    def _parse_file_robust(self, filename: str, progress_callback: Optional[ProgressCallback] = None) -> Dict[str, RegionData]:
        """
        更稳健的文件解析方法，完全按照原始格式解析
        基于正则表达式精确分割区块
        """
        if progress_callback:
            progress_callback.update(10, "正在读取文件...")

        # 读取文件内容，尝试多种编码
        content = None
        for encoding in self.encoding_options:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件失败 (编码: {encoding}): {e}")
                continue

        if content is None:
            raise ValueError("无法读取文件，尝试了所有编码选项")

        if progress_callback:
            progress_callback.update(20, "文件读取完成，开始解析...")

        # 使用正则表达式精确分割区块
        blocks = re.split(r'\[Name\]\s*', content)[1:]

        if not blocks:
            raise ValueError("未找到任何区域数据块")

        regions = {}
        offset = 0
        total_blocks = len(blocks)

        for i, block in enumerate(blocks):
            if progress_callback and progress_callback.is_cancelled():
                return {}

            # 更新进度
            if progress_callback:
                progress = 20 + int((i / total_blocks) * 70)
                progress_callback.update(progress, f"处理区域数据 {i+1}/{total_blocks}")

            # 使用更安全的方式分割数据段
            data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
            if len(data_parts) < 2:
                continue
            header = data_parts[0].strip()
            name = header.splitlines()[0].strip()
            # name = data_parts[0].strip()
            remaining = data_parts[1]

            # 分割面和数据
            face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
            if len(face_parts) < 2:
                data_section = face_parts[0]
                faces_section = ''
            else:
                data_section = face_parts[0]
                faces_section = face_parts[1]

            # 处理数据部分
            data_lines = [line.strip() for line in data_section.split('\n')
                         if line.strip()]
            if not data_lines:
                continue

            header = [h.strip() for h in data_lines[0].split(',')]

            try:
                data = np.array([list(map(float, line.split(',')))
                               for line in data_lines[1:]])
                df = pd.DataFrame(data, columns=header)

                # 创建区域数据
                region_data = RegionData(name, df)

                # 处理面数据
                faces = []
                ori_faces = []
                for line in faces_section.split('\n'):
                    line = line.strip()
                    if line:
                        try:
                            ori_face = list(map(int, line.split(',')))
                            face = [(it + offset) for it in ori_face]
                            ori_faces.append(ori_face)
                            faces.append(face)
                        except (ValueError, IndexError):
                            continue

                region_data.set_faces(ori_faces)

                offset += len(data)
                regions[name] = region_data

                print(f"区域 {name}: 解析完成，数据点 {len(data)}，面片 {len(faces)}")

            except Exception as e:
                print(f"解析区域 {name} 失败: {e}")
                continue

        print(f"稳健解析完成，共处理了 {len(regions)} 个区域")
        return regions

    def _read_file_with_encoding(self, filename: str) -> Optional[List[str]]:
        """尝试不同编码读取文件"""
        for encoding in self.encoding_options:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    return f.readlines()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件失败 (编码: {encoding}): {e}")
                continue
        return None
        
    def _parse_multi_region(self, lines: List[str], progress_callback: Optional[ProgressCallback] = None) -> Dict[str, RegionData]:
        """解析多区域格式"""
        regions = {}
        current_region = None
        i = 0
        total_lines = len(lines)
        
        while i < len(lines):
            if progress_callback and progress_callback.is_cancelled():
                return {}
                
            # 更新进度
            if progress_callback and i % 10000 == 0:
                progress = 15 + int((i / total_lines) * 60)
                progress_callback.update(progress, f"正在解析... ({i}/{total_lines})")
                
            line = lines[i].strip()
            
            # 查找区域名称
            if line.startswith('[Name]') and i + 1 < len(lines):
                current_region = lines[i + 1].strip()
                if progress_callback:
                    progress = 15 + int((i / total_lines) * 60)
                    progress_callback.update(progress, f'名称:{current_region}', f"找到区域: {current_region}")
                print(f"找到区域: {current_region}")
                i += 2
                continue

            # 跳过[Spatial Fields]段
            if line.startswith('[Spatial Fields]') and current_region:
                if progress_callback:
                    progress = 15 + int((i / total_lines) * 60)
                    progress_callback.update(progress, f'{current_region}', f"跳过区域 {current_region} 的空间字段...")
                # 跳过到下一个段落
                i += 1
                while i < len(lines) and not lines[i].strip().startswith('['):
                    i += 1
                continue

            # 查找数据段
            if line.startswith('[Data]') and current_region:
                if progress_callback:
                    progress = 15 + int((i / total_lines) * 60)
                    progress_callback.update(progress, f'载入{current_region}点', f"解析区域 {current_region} 的数据...")

                region_data = self._parse_region_data_section(lines, i, current_region, progress_callback)
                if region_data:
                    if current_region not in regions:
                        regions[current_region] = RegionData(current_region, region_data['data'])
                    else:
                        regions[current_region].data = region_data['data']
                    i = region_data['next_index']
                else:
                    i += 1
                continue

            # 查找面片段
            if line.startswith('[Faces]') and current_region:
                if progress_callback:
                    progress = 15 + int((i / total_lines) * 60)
                    progress_callback.update(progress, f'载入{current_region}面片', f"解析区域 {current_region} 的面片...")

                faces_data = self._parse_faces_section(lines, i, current_region, progress_callback)
                if faces_data:
                    if current_region in regions:
                        regions[current_region].set_faces(faces_data['faces'])
                        print(f"区域 {current_region}: 设置了 {len(faces_data['faces'])} 个面片")
                    i = faces_data['next_index']
                else:
                    i += 1
                continue

            # 检查区域结束标记
            if line.startswith('#-- End of profile') and current_region:
                if progress_callback:
                    progress = 15 + int((i / total_lines) * 60)
                    progress_callback.update(progress, f'{current_region}', f"区域 {current_region} 解析完成")
                print(f"区域 {current_region} 解析完成")
                current_region = None  # 重置当前区域
                i += 1
                continue
                
            i += 1
            
        print(f"多区域解析完成，共找到 {len(regions)} 个区域: {list(regions.keys())}")
        return regions
        
    def _parse_region_data_section(self, lines: List[str], start_index: int, region_name: str, 
                                 progress_callback: Optional[ProgressCallback] = None) -> Optional[Dict[str, Any]]:
        """解析区域数据段"""
        try:
            if start_index + 1 >= len(lines):
                return None
                
            # 解析列名
            header = lines[start_index + 1].strip()
            columns = [col.strip() for col in header.split(',')]
            expected_cols = len(columns)
            
            # 读取数据
            data = []
            valid_count = 0
            skip_count = 0
            j = start_index + 2
            
            while j < len(lines):
                if progress_callback and progress_callback.is_cancelled():
                    return None
                    
                data_line = lines[j].strip()
                
                # 检查是否到达段落结束
                if (data_line.startswith('[') or
                    data_line.startswith('#-- End of profile') or
                    not data_line):
                    if data_line.startswith('['):
                        # 遇到新段落，停止解析数据
                        break
                    elif data_line.startswith('#-- End of profile'):
                        # 遇到区域结束标记，停止解析
                        break
                    else:
                        # 空行，跳过
                        j += 1
                        continue
                    
                # 解析数据行
                if self._parse_data_line(data_line, expected_cols, data):
                    valid_count += 1
                    
                    # 显示进度
                    if progress_callback and valid_count % 5000 == 0:
                        progress = 15 + int((j / len(lines)) * 60)
                        # print('parse region', progress)
                        progress_callback.update(progress, f'{region_name}',
                            f"区域 {region_name}: 已读取 {valid_count} 行数据...")
                else:
                    skip_count += 1
                    
                j += 1
                
            if data:
                df = pd.DataFrame(data, columns=columns)
                print(f"区域 {region_name}: 解析完成，有效数据 {valid_count} 行，跳过 {skip_count} 行")
                return {'data': df, 'next_index': j}
                
            return None
            
        except Exception as e:
            print(f"解析区域 {region_name} 数据失败: {e}")
            return None

    def _parse_data_line(self, data_line: str, expected_cols: int, data: List[List[float]]) -> bool:
        """解析单行数据"""
        try:
            values_str = data_line.split(',')

            if len(values_str) == expected_cols:
                values = []
                valid_row = True

                for val_str in values_str:
                    val_str = val_str.strip()
                    if val_str:
                        try:
                            values.append(float(val_str))
                        except ValueError:
                            valid_row = False
                            break
                    else:
                        values.append(0.0)

                if valid_row:
                    data.append(values)
                    return True

            return False

        except Exception:
            return False

    def _parse_faces_section(self, lines: List[str], start_index: int, region_name: str,
                           progress_callback: Optional[ProgressCallback] = None) -> Optional[Dict[str, Any]]:
        """解析面片数据段"""
        try:
            faces = []
            valid_count = 0
            invalid_count = 0
            j = start_index + 1

            print(f"区域 {region_name}: 开始解析面片数据，起始行 {start_index + 1}")

            while j < len(lines):
                if progress_callback and progress_callback.is_cancelled():
                    return None

                face_line = lines[j].strip()

                # 检查是否到达段落结束
                if (face_line.startswith('[') or
                    face_line.startswith('#-- End of profile') or
                    not face_line):
                    if face_line.startswith('['):
                        # 遇到新段落，停止解析面片
                        print(f"区域 {region_name}: 遇到新段落 '{face_line}'，停止解析面片")
                        break
                    elif face_line.startswith('#-- End of profile'):
                        # 遇到区域结束标记，停止解析
                        print(f"区域 {region_name}: 遇到区域结束标记，停止解析面片")
                        break
                    else:
                        # 空行，跳过
                        j += 1
                        continue

                # 解析面片索引
                if self._parse_face_line(face_line, faces):
                    valid_count += 1
                else:
                    invalid_count += 1
                    # 显示前几个无效行的内容用于调试
                    if invalid_count <= 3:
                        print(f"区域 {region_name}: 无效面片行 {j}: '{face_line}'")

                # 显示进度
                if progress_callback and valid_count % 5000 == 0:
                    progress = 15 + int((j / len(lines)) * 60)
                    progress_callback.update(progress, f'{region_name}',
                        f"区域 {region_name}: 已读取 {valid_count} 个面片...")

                j += 1

            print(f"区域 {region_name}: 面片解析完成 - 有效: {valid_count}, 无效: {invalid_count}")

            if faces:
                # 显示前几个面片用于调试
                print(f"区域 {region_name}: 前3个面片: {faces[:3]}")
                return {'faces': faces, 'next_index': j}

            return None

        except Exception as e:
            print(f"解析区域 {region_name} 面片失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _parse_face_line(self, face_line: str, faces: List[List[int]]) -> bool:
        """解析单行面片数据"""
        try:
            # 移除多余的空格和制表符
            face_line = face_line.strip()
            if not face_line:
                return False

            # 分割索引字符串
            indices_str = face_line.split(',')
            indices = []

            for idx_str in indices_str:
                idx_str = idx_str.strip()
                if idx_str:
                    try:
                        idx = int(idx_str)
                        # 确保索引非负
                        if idx >= 0:
                            indices.append(idx)
                    except ValueError:
                        # 跳过无法转换为整数的值
                        continue

            if len(indices) >= 3:  # 至少需要3个点构成面
                faces.append(indices)
                return True

            return False

        except Exception as e:
            # 调试信息
            print(f"解析面片行失败: '{face_line}' - {e}")
            return False



    def _parse_single_region(self, lines: List[str]) -> Optional[RegionData]:
        """解析单区域格式（回退方案）"""
        try:
            # 查找数据开始行
            data_start = 0
            header_line = None

            for i, line in enumerate(lines):
                line_stripped = line.strip()
                if line_stripped.startswith('[Data]'):
                    header_line = i + 1
                    data_start = i + 2
                    break

            if header_line is None:
                return None

            # 解析列名
            header = lines[header_line].strip()
            columns = [col.strip() for col in header.split(',')]
            expected_cols = len(columns)

            # 读取数据
            data = []
            valid_count = 0

            for line in lines[data_start:]:
                line = line.strip()
                if line and not line.startswith('[') and not line.startswith('#'):
                    if self._parse_data_line(line, expected_cols, data):
                        valid_count += 1

            if not data:
                return None

            df = pd.DataFrame(data, columns=columns)
            region_data = RegionData("Default", df)

            # 查找面片数据
            faces_start = None
            for i, line in enumerate(lines):
                if line.strip().startswith('[Faces]'):
                    faces_start = i + 1
                    break

            if faces_start is not None:
                faces = []
                for line in lines[faces_start:]:
                    line = line.strip()
                    if (line.startswith('[') or
                        line.startswith('#-- End of profile') or
                        not line):
                        if line.startswith('[') or line.startswith('#-- End of profile'):
                            break
                        continue

                    self._parse_face_line(line, faces)

                if faces:
                    region_data.set_faces(faces)
                    print(f"单区域: 设置了 {len(faces)} 个面片")

            print(f"单区域解析完成: {len(df)} 行数据")
            return region_data

        except Exception as e:
            print(f"单区域解析失败: {e}")
            return None
